import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Image, SafeAreaView, Text, TouchableOpacity, View } from 'react-native';
import i18n from '../i18n';

const SplashScreen2 = () => {
  const navigation = useNavigation();

  const handleNext = () => {
    navigation.navigate('SplashScreen3' as never);
  };

  const handleSkip = () => {
    navigation.navigate('Home' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Skip Button */}
      <View className="flex-row justify-end p-4">
        <TouchableOpacity onPress={handleSkip} className="px-4 py-2">
          <Text className="text-gray-600 text-base font-medium">
            {i18n.t('buttons.skip')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View className="flex-1 justify-center items-center px-8">
        {/* Image */}
        <View className="mb-12">
          <Image
            source={require('../assets/images/icon.png')}
            className="w-64 h-64"
            resizeMode="contain"
          />
        </View>

        {/* Heading */}
        <Text className="text-3xl font-bold text-gray-900 text-center mb-6">
          {i18n.t('splash2.heading')}
        </Text>

        {/* Paragraph */}
        <Text className="text-lg text-gray-600 text-center leading-6 mb-12">
          {i18n.t('splash2.paragraph')}
        </Text>
      </View>

      {/* Next Button */}
      <View className="p-6">
        <TouchableOpacity
          onPress={handleNext}
          className="bg-primary rounded-lg py-4 px-8 shadow-lg"
          activeOpacity={0.8}
        >
          <Text className="text-white text-lg font-semibold text-center">
            {i18n.t('buttons.next')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default SplashScreen2;

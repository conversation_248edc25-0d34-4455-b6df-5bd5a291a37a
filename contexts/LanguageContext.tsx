import React, { createContext, useContext, useState, ReactNode } from 'react';
import i18n from '../i18n';

interface LanguageContextType {
  currentLanguage: string;
  switchLanguage: (language: string) => void;
  availableLanguages: { code: string; name: string }[];
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('it'); // Default to Italian

  const availableLanguages = [
    { code: 'it', name: 'Italiano' },
    { code: 'en', name: 'English' },
  ];

  const switchLanguage = (language: string) => {
    setCurrentLanguage(language);
    i18n.locale = language;
  };

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        switchLanguage,
        availableLanguages,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

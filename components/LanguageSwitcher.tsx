import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';

const LanguageSwitcher = () => {
  const { currentLanguage, switchLanguage, availableLanguages } = useLanguage();

  return (
    <View style={styles.container}>
      {availableLanguages.map((language) => (
        <TouchableOpacity
          key={language.code}
          onPress={() => switchLanguage(language.code)}
          style={[
            styles.button,
            currentLanguage === language.code ? styles.activeButton : styles.inactiveButton
          ]}
        >
          <Text
            style={[
              styles.text,
              currentLanguage === language.code ? styles.activeText : styles.inactiveText
            ]}
          >
            {language.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  activeButton: {
    backgroundColor: '#F54619',
  },
  inactiveButton: {
    backgroundColor: 'transparent',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeText: {
    color: 'white',
  },
  inactiveText: {
    color: '#6B7280',
  },
});

export default LanguageSwitcher;

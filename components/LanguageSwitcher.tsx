import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';

const LanguageSwitcher = () => {
  const { currentLanguage, switchLanguage, availableLanguages } = useLanguage();

  return (
    <View className="flex-row bg-gray-100 rounded-lg p-1">
      {availableLanguages.map((language) => (
        <TouchableOpacity
          key={language.code}
          onPress={() => switchLanguage(language.code)}
          className={`px-3 py-2 rounded-md ${
            currentLanguage === language.code
              ? 'bg-primary'
              : 'bg-transparent'
          }`}
        >
          <Text
            className={`text-sm font-medium ${
              currentLanguage === language.code
                ? 'text-white'
                : 'text-gray-600'
            }`}
          >
            {language.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default LanguageSwitcher;

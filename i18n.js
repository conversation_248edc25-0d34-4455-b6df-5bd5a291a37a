import { I18n } from 'i18n-js';

const i18n = new I18n({
  en: {
    // Splash Screen 1
    splash1: {
      heading: "Welcome to ControllOne",
      paragraph: "Your complete solution for real-time fleet monitoring, control, and analytics."
    },
    // Splash Screen 2
    splash2: {
      heading: "Manage Your Vehicles Efficiently",
      paragraph: "Track, lock, and optimise routes with detailed analytics and alerts."
    },
    // Splash Screen 3
    splash3: {
      heading: "Smart Reports & Geofencing",
      paragraph: "Export trip reports, monitor geofences, and receive smart alerts."
    },
    // Common buttons
    buttons: {
      next: "Next",
      skip: "Skip",
      getStarted: "Get Started"
    }
  },
  it: {
    // Splash Screen 1
    splash1: {
      heading: "Benvenuto in ControllOne",
      paragraph: "La tua soluzione completa per il monitoraggio, il controllo e l'analisi della flotta in tempo reale."
    },
    // Splash Screen 2
    splash2: {
      heading: "Gestisci i tuoi veicoli in modo efficiente",
      paragraph: "Traccia, blocca e ottimizza i percorsi con analisi dettagliate e avvisi."
    },
    // Splash Screen 3
    splash3: {
      heading: "Report intelligenti e geofencing",
      paragraph: "Esporta report di viaggio, monitora le geofence e ricevi avvisi intelligenti."
    },
    // Common buttons
    buttons: {
      next: "Avanti",
      skip: "Salta",
      getStarted: "Inizia"
    }
  }
});

// Set default locale to Italian
i18n.defaultLocale = 'it';
i18n.locale = 'it';

export default i18n;
